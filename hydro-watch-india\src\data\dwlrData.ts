// DWLR Station Data - Digital Water Level Recorder Network
// Realistic groundwater monitoring data across India

export interface DWLRStation {
  id: string;
  dwlrId: string;
  name: string;
  location: string;
  district: string;
  state: string;
  coordinates: {
    lat: number;
    lng: number;
  };
  // Water level data
  currentLevel: number;
  staticWaterLevel: number;
  depthToWater: number;
  wellDepth: number;
  // Status and thresholds
  status: 'safe' | 'moderate' | 'critical' | 'dry';
  safeThreshold: number;
  criticalThreshold: number;
  // Aquifer information
  aquiferType: 'alluvial' | 'hard_rock' | 'coastal' | 'volcanic';
  aquiferDepth: number;
  // Station metadata
  installationDate: string;
  lastUpdated: string;
  dataFrequency: 'hourly' | '6hourly' | 'daily';
  // Water quality (basic parameters)
  waterQuality: {
    ph: number;
    tds: number; // Total Dissolved Solids (mg/L)
    ec: number; // Electrical Conductivity (μS/cm)
    chloride: number; // mg/L
    quality: 'excellent' | 'good' | 'acceptable' | 'poor';
  };
  // Trend analysis
  trend: 'rising' | 'stable' | 'declining';
  monthlyChange: number; // in meters
  yearlyChange: number; // in meters
  rechargeStatus: 'active' | 'moderate' | 'poor' | 'none';
}

export interface WaterLevelReading {
  timestamp: string;
  level: number;
  temperature?: number;
  batteryLevel?: number;
}

export const dwlrStations: DWLRStation[] = [
  {
    id: 'DWLR_001',
    dwlrId: 'RJ-JAI-001',
    name: 'Jaipur DWLR Station',
    location: 'Sanganer Block, Jaipur',
    district: 'Jaipur',
    state: 'Rajasthan',
    coordinates: { lat: 26.8467, lng: 75.8056 },
    currentLevel: 12.45,
    staticWaterLevel: 11.80,
    depthToWater: 24.55,
    wellDepth: 45.0,
    status: 'moderate',
    safeThreshold: 15.0,
    criticalThreshold: 8.0,
    aquiferType: 'alluvial',
    aquiferDepth: 65.0,
    installationDate: '2019-03-15',
    lastUpdated: '2024-01-15T14:30:00Z',
    dataFrequency: 'hourly',
    waterQuality: {
      ph: 7.2,
      tds: 480,
      ec: 750,
      chloride: 85,
      quality: 'good'
    },
    trend: 'declining',
    monthlyChange: -0.15,
    yearlyChange: -1.2,
    rechargeStatus: 'poor'
  },
  {
    id: 'DWLR_002',
    dwlrId: 'GJ-AHM-002',
    name: 'Ahmedabad DWLR Station',
    location: 'Vatva Industrial Area',
    district: 'Ahmedabad',
    state: 'Gujarat',
    coordinates: { lat: 23.0225, lng: 72.5714 },
    currentLevel: 8.20,
    staticWaterLevel: 7.95,
    depthToWater: 31.80,
    wellDepth: 55.0,
    status: 'critical',
    safeThreshold: 12.0,
    criticalThreshold: 6.0,
    aquiferType: 'alluvial',
    aquiferDepth: 80.0,
    installationDate: '2018-11-20',
    lastUpdated: '2024-01-15T14:15:00Z',
    dataFrequency: 'hourly',
    waterQuality: {
      ph: 6.8,
      tds: 1250,
      ec: 1950,
      chloride: 320,
      quality: 'poor'
    },
    trend: 'declining',
    monthlyChange: -0.25,
    yearlyChange: -2.1,
    rechargeStatus: 'none'
  },
  {
    id: 'DWLR_003',
    dwlrId: 'PB-LDH-003',
    name: 'Ludhiana DWLR Station',
    location: 'Khanna Block, Ludhiana',
    district: 'Ludhiana',
    state: 'Punjab',
    coordinates: { lat: 30.9010, lng: 75.8573 },
    currentLevel: 6.75,
    staticWaterLevel: 6.20,
    depthToWater: 18.25,
    wellDepth: 35.0,
    status: 'critical',
    safeThreshold: 10.0,
    criticalThreshold: 5.0,
    aquiferType: 'alluvial',
    aquiferDepth: 45.0,
    installationDate: '2020-01-10',
    lastUpdated: '2024-01-15T14:45:00Z',
    dataFrequency: 'hourly',
    waterQuality: {
      ph: 7.8,
      tds: 680,
      ec: 1100,
      chloride: 145,
      quality: 'acceptable'
    },
    trend: 'declining',
    monthlyChange: -0.08,
    yearlyChange: -0.95,
    rechargeStatus: 'poor'
  },
  {
    id: 'DWLR_004',
    dwlrId: 'MH-PUN-004',
    name: 'Pune DWLR Station',
    location: 'Khed Taluka, Pune Rural',
    district: 'Pune',
    state: 'Maharashtra',
    coordinates: { lat: 18.5204, lng: 73.8567 },
    currentLevel: 18.90,
    staticWaterLevel: 17.65,
    depthToWater: 16.10,
    wellDepth: 42.0,
    status: 'safe',
    safeThreshold: 15.0,
    criticalThreshold: 8.0,
    aquiferType: 'hard_rock',
    aquiferDepth: 55.0,
    installationDate: '2019-08-25',
    lastUpdated: '2024-01-15T14:20:00Z',
    dataFrequency: 'hourly',
    waterQuality: {
      ph: 7.4,
      tds: 320,
      ec: 520,
      chloride: 55,
      quality: 'excellent'
    },
    trend: 'stable',
    monthlyChange: 0.02,
    yearlyChange: 0.15,
    rechargeStatus: 'active'
  },
  {
    id: 'DWLR_005',
    dwlrId: 'TN-CHE-005',
    name: 'Chennai DWLR Station',
    location: 'Ambattur Industrial Estate',
    district: 'Chennai',
    state: 'Tamil Nadu',
    coordinates: { lat: 13.0827, lng: 80.2707 },
    currentLevel: 4.25,
    staticWaterLevel: 3.80,
    depthToWater: 8.75,
    wellDepth: 25.0,
    status: 'critical',
    safeThreshold: 8.0,
    criticalThreshold: 3.0,
    aquiferType: 'coastal',
    aquiferDepth: 35.0,
    installationDate: '2021-02-14',
    lastUpdated: '2024-01-15T14:10:00Z',
    dataFrequency: 'hourly',
    waterQuality: {
      ph: 6.5,
      tds: 2200,
      ec: 3500,
      chloride: 890,
      quality: 'poor'
    },
    trend: 'declining',
    monthlyChange: -0.18,
    yearlyChange: -1.8,
    rechargeStatus: 'none'
  },
  {
    id: 'DWLR_006',
    dwlrId: 'KA-BLR-006',
    name: 'Bangalore DWLR Station',
    location: 'Whitefield Tech Park',
    district: 'Bengaluru Urban',
    state: 'Karnataka',
    coordinates: { lat: 12.9716, lng: 77.5946 },
    currentLevel: 22.30,
    staticWaterLevel: 21.45,
    depthToWater: 28.70,
    wellDepth: 65.0,
    status: 'safe',
    safeThreshold: 18.0,
    criticalThreshold: 12.0,
    aquiferType: 'hard_rock',
    aquiferDepth: 85.0,
    installationDate: '2020-06-30',
    lastUpdated: '2024-01-15T14:25:00Z',
    dataFrequency: 'hourly',
    waterQuality: {
      ph: 7.1,
      tds: 410,
      ec: 680,
      chloride: 78,
      quality: 'good'
    },
    trend: 'rising',
    monthlyChange: 0.12,
    yearlyChange: 0.85,
    rechargeStatus: 'active'
  }
];

// Generate realistic hourly data for the last 24 hours
export const generateHourlyData = (station: DWLRStation): WaterLevelReading[] => {
  const data: WaterLevelReading[] = [];
  const now = new Date();
  const baseLevel = station.currentLevel;
  const trend = station.trend === 'rising' ? 0.02 : station.trend === 'declining' ? -0.02 : 0;
  
  for (let i = 23; i >= 0; i--) {
    const timestamp = new Date(now.getTime() - i * 60 * 60 * 1000);
    const seasonalVariation = Math.sin((timestamp.getHours() / 24) * Math.PI * 2) * 0.05;
    const randomVariation = (Math.random() - 0.5) * 0.1;
    const trendEffect = trend * (23 - i) / 24;
    
    data.push({
      timestamp: timestamp.toISOString(),
      level: Number((baseLevel + seasonalVariation + randomVariation + trendEffect).toFixed(2)),
      temperature: 18 + Math.sin((timestamp.getHours() / 24) * Math.PI * 2) * 8 + Math.random() * 2,
      batteryLevel: 85 + Math.random() * 10
    });
  }
  
  return data;
};

// Generate weekly data
export const generateWeeklyData = (station: DWLRStation): WaterLevelReading[] => {
  const data: WaterLevelReading[] = [];
  const now = new Date();
  const baseLevel = station.currentLevel;
  const trend = station.trend === 'rising' ? 0.01 : station.trend === 'declining' ? -0.01 : 0;
  
  for (let i = 6; i >= 0; i--) {
    const timestamp = new Date(now.getTime() - i * 24 * 60 * 60 * 1000);
    const randomVariation = (Math.random() - 0.5) * 0.2;
    const trendEffect = trend * (6 - i);
    
    data.push({
      timestamp: timestamp.toISOString(),
      level: Number((baseLevel + randomVariation + trendEffect).toFixed(2)),
      temperature: 22 + Math.random() * 4,
      batteryLevel: 80 + Math.random() * 15
    });
  }
  
  return data;
};

// Get station statistics
export const getNetworkStatistics = () => {
  const total = dwlrStations.length;
  const online = dwlrStations.filter(s => s.status !== 'dry').length;
  const safe = dwlrStations.filter(s => s.status === 'safe').length;
  const moderate = dwlrStations.filter(s => s.status === 'moderate').length;
  const critical = dwlrStations.filter(s => s.status === 'critical').length;
  const dry = dwlrStations.filter(s => s.status === 'dry').length;
  const avgLevel = dwlrStations.reduce((sum, s) => sum + s.currentLevel, 0) / total;
  const declining = dwlrStations.filter(s => s.trend === 'declining').length;
  const poorRecharge = dwlrStations.filter(s => s.rechargeStatus === 'poor' || s.rechargeStatus === 'none').length;
  
  return {
    total,
    online,
    safe,
    moderate, 
    critical,
    dry,
    avgLevel: Number(avgLevel.toFixed(2)),
    declining,
    poorRecharge,
    states: [...new Set(dwlrStations.map(s => s.state))].length,
    districts: [...new Set(dwlrStations.map(s => s.district))].length
  };
};