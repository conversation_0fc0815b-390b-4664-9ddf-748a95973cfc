import { Card } from "@/components/ui/card";
import { Badge } from "@/components/ui/badge";
import { Progress } from "@/components/ui/progress";
import { Beaker, Droplets, Zap, TestTube } from "lucide-react";

interface WaterQualityData {
  ph: number;
  tds: number;
  ec: number;
  chloride: number;
  quality: 'excellent' | 'good' | 'acceptable' | 'poor';
}

interface WaterQualityCardProps {
  data: WaterQualityData;
}

export const WaterQualityCard = ({ data }: WaterQualityCardProps) => {
  const getQualityColor = (quality: string) => {
    switch (quality) {
      case 'excellent': return 'success';
      case 'good': return 'success';
      case 'acceptable': return 'warning';
      case 'poor': return 'critical';
      default: return 'default';
    }
  };

  const getQualityScore = (quality: string) => {
    switch (quality) {
      case 'excellent': return 95;
      case 'good': return 75;
      case 'acceptable': return 50;
      case 'poor': return 25;
      default: return 0;
    }
  };

  const getTDSStatus = (tds: number) => {
    if (tds <= 300) return { status: 'Excellent', color: 'success' };
    if (tds <= 600) return { status: 'Good', color: 'success' };
    if (tds <= 900) return { status: 'Fair', color: 'warning' };
    if (tds <= 1200) return { status: 'Poor', color: 'warning' };
    return { status: 'Unacceptable', color: 'critical' };
  };

  const getPHStatus = (ph: number) => {
    if (ph >= 6.5 && ph <= 8.5) return { status: 'Normal', color: 'success' };
    if (ph < 6.5) return { status: 'Acidic', color: 'warning' };
    return { status: 'Alkaline', color: 'warning' };
  };

  const tdsStatus = getTDSStatus(data.tds);
  const phStatus = getPHStatus(data.ph);
  const qualityScore = getQualityScore(data.quality);

  return (
    <Card className="p-4 shadow-soft">
      <div className="flex items-center justify-between mb-4">
        <div className="flex items-center">
          <Beaker className="w-5 h-5 text-primary mr-2" />
          <h3 className="text-lg font-semibold">Water Quality</h3>
        </div>
        <Badge 
          variant="outline" 
          className={`bg-${getQualityColor(data.quality)}/10 text-${getQualityColor(data.quality)} capitalize`}
        >
          {data.quality}
        </Badge>
      </div>

      {/* Overall Quality Score */}
      <div className="mb-6">
        <div className="flex items-center justify-between mb-2">
          <span className="text-sm font-medium">Overall Quality</span>
          <span className="text-sm font-bold">{qualityScore}%</span>
        </div>
        <Progress value={qualityScore} className="h-2" />
      </div>

      {/* Quality Parameters */}
      <div className="space-y-4">
        {/* pH Level */}
        <div className="flex items-center justify-between p-3 bg-accent/5 rounded-lg">
          <div className="flex items-center">
            <TestTube className="w-4 h-4 text-primary mr-2" />
            <div>
              <p className="text-sm font-medium">pH Level</p>
              <p className="text-xs text-muted-foreground">Acidity/Alkalinity</p>
            </div>
          </div>
          <div className="text-right">
            <p className="text-sm font-bold">{data.ph}</p>
            <Badge variant="outline" className={`text-xs bg-${phStatus.color}/10 text-${phStatus.color}`}>
              {phStatus.status}
            </Badge>
          </div>
        </div>

        {/* TDS */}
        <div className="flex items-center justify-between p-3 bg-accent/5 rounded-lg">
          <div className="flex items-center">
            <Droplets className="w-4 h-4 text-primary mr-2" />
            <div>
              <p className="text-sm font-medium">TDS</p>
              <p className="text-xs text-muted-foreground">Total Dissolved Solids</p>
            </div>
          </div>
          <div className="text-right">
            <p className="text-sm font-bold">{data.tds} mg/L</p>
            <Badge variant="outline" className={`text-xs bg-${tdsStatus.color}/10 text-${tdsStatus.color}`}>
              {tdsStatus.status}
            </Badge>
          </div>
        </div>

        {/* Electrical Conductivity */}
        <div className="flex items-center justify-between p-3 bg-accent/5 rounded-lg">
          <div className="flex items-center">
            <Zap className="w-4 h-4 text-primary mr-2" />
            <div>
              <p className="text-sm font-medium">EC</p>
              <p className="text-xs text-muted-foreground">Electrical Conductivity</p>
            </div>
          </div>
          <div className="text-right">
            <p className="text-sm font-bold">{data.ec} μS/cm</p>
          </div>
        </div>

        {/* Chloride */}
        <div className="flex items-center justify-between p-3 bg-accent/5 rounded-lg">
          <div className="flex items-center">
            <div className="w-4 h-4 bg-primary rounded-full mr-2" />
            <div>
              <p className="text-sm font-medium">Chloride</p>
              <p className="text-xs text-muted-foreground">Salt Content</p>
            </div>
          </div>
          <div className="text-right">
            <p className="text-sm font-bold">{data.chloride} mg/L</p>
            <Badge variant="outline" className={`text-xs ${data.chloride > 250 ? 'bg-warning/10 text-warning' : 'bg-success/10 text-success'}`}>
              {data.chloride > 250 ? 'High' : 'Normal'}
            </Badge>
          </div>
        </div>
      </div>

      {/* Quality Guidelines */}
      <div className="mt-4 p-3 bg-gradient-subtle rounded-lg">
        <p className="text-xs text-muted-foreground text-center">
          Quality based on BIS 10500:2012 standards
        </p>
      </div>
    </Card>
  );
};