import { useState, useEffect } from "react";
import { SummaryCard } from "@/components/SummaryCard";
import { StationCard } from "@/components/StationCard";
import { SimpleChart } from "@/components/SimpleChart";
import { AlertBanner } from "@/components/AlertBanner";
import { IndiaMap } from "@/components/IndiaMap";
import { Card } from "@/components/ui/card";
import { Button } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Badge } from "@/components/ui/badge";
import { 
  Droplets, 
  MapPin, 
  Activity, 
  AlertTriangle, 
  Search,
  Navigation,
  Filter,
  Bell,
  Database,
  Zap,
  Layers
} from "lucide-react";
import { dwlrStations, getNetworkStatistics, generateHourlyData } from "@/data/dwlrData";

export const Dashboard = () => {
  const [searchQuery, setSearchQuery] = useState('');
  const [filteredStations, setFilteredStations] = useState(dwlrStations);
  const [selectedStation, setSelectedStation] = useState<string | undefined>();
  const stats = getNetworkStatistics();

  useEffect(() => {
    const filtered = dwlrStations.filter(station =>
      station.name.toLowerCase().includes(searchQuery.toLowerCase()) ||
      station.location.toLowerCase().includes(searchQuery.toLowerCase()) ||
      station.district.toLowerCase().includes(searchQuery.toLowerCase()) ||
      station.state.toLowerCase().includes(searchQuery.toLowerCase())
    );
    setFilteredStations(filtered);
  }, [searchQuery]);

  // Get sample chart data from the first station
  const sampleStation = dwlrStations[0];
  const chartData = generateHourlyData(sampleStation).slice(-8).map(reading => ({
    time: new Date(reading.timestamp).toTimeString().slice(0, 5),
    level: reading.level,
    temperature: reading.temperature
  }));

  return (
    <div className="min-h-screen bg-background">
      {/* Header */}
      <div className="bg-gradient-primary text-white p-4">
        <div className="flex items-center justify-between mb-4">
          <div className="flex items-center">
            <Droplets className="w-8 h-8 mr-3" />
            <div>
              <h1 className="text-xl font-bold gradient-text">India Groundwater Status</h1>
              <p className="text-sm opacity-90">Real-time monitoring dashboard</p>
            </div>
          </div>
          <Button variant="outline" size="sm" className="text-white border-white/30 hover:bg-white/10 hover:border-white/50 hover:shadow-lg hover:shadow-white/25">
            <Bell className="w-4 h-4 mr-2" />
            Alerts
          </Button>
        </div>

        {/* Quick Actions */}
        <div className="flex gap-2 overflow-x-auto pb-2">
          <Button variant="outline" size="sm" className="text-white border-white/30 whitespace-nowrap hover:bg-white/10 hover:border-white/50 hover:shadow-lg hover:shadow-white/25">
            <Navigation className="w-4 h-4 mr-2" />
            Find Nearest
          </Button>
          <Button variant="outline" size="sm" className="text-white border-white/30 whitespace-nowrap hover:bg-white/10 hover:border-white/50 hover:shadow-lg hover:shadow-white/25">
            <MapPin className="w-4 h-4 mr-2" />
            My District
          </Button>
          <Button variant="outline" size="sm" className="text-white border-white/30 whitespace-nowrap hover:bg-white/10 hover:border-white/50 hover:shadow-lg hover:shadow-white/25">
            <Filter className="w-4 h-4 mr-2" />
            Filters
          </Button>
        </div>
      </div>

      {/* Alert Banner */}
      <AlertBanner />

      <div className="p-4 space-y-6">
        {/* Enhanced Summary Cards */}
        <div className="grid grid-cols-2 md:grid-cols-4 gap-4">
          <SummaryCard
            title="DWLR Stations"
            value={stats.total}
            subtitle={`${stats.states} states, ${stats.districts} districts`}
            icon={Database}
            variant="default"
          />
          <SummaryCard
            title="Online Status"
            value={stats.online}
            subtitle={`${((stats.online/stats.total)*100).toFixed(0)}% operational`}
            icon={Zap}
            variant="success"
          />
          <SummaryCard
            title="Avg Water Level"
            value={`${stats.avgLevel}m`}
            subtitle="Network average"
            icon={Droplets}
            variant="default"
            trend="stable"
          />
          <SummaryCard
            title="Critical Status"
            value={stats.critical + stats.dry}
            subtitle={`${stats.declining} declining trend`}
            icon={AlertTriangle}
            variant="critical"
          />
        </div>

        {/* Additional Statistics */}
        <div className="grid grid-cols-3 gap-4">
          <Card className="p-4 shadow-soft card-hover-glow">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-sm text-muted-foreground">Safe Levels</p>
                <p className="text-2xl font-bold gradient-text-success">{stats.safe}</p>
              </div>
              <div className="p-2 bg-success/10 rounded-lg status-glow">
                <Droplets className="w-5 h-5 text-success" />
              </div>
            </div>
          </Card>
          
          <Card className="p-4 shadow-soft card-hover-glow">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-sm text-muted-foreground">Moderate Risk</p>
                <p className="text-2xl font-bold gradient-text-warning">{stats.moderate}</p>
              </div>
              <div className="p-2 bg-warning/10 rounded-lg status-glow">
                <AlertTriangle className="w-5 h-5 text-warning" />
              </div>
            </div>
          </Card>
          
          <Card className="p-4 shadow-soft card-hover-glow">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-sm text-muted-foreground">Poor Recharge</p>
                <p className="text-2xl font-bold gradient-text-critical">{stats.poorRecharge}</p>
              </div>
              <div className="p-2 bg-critical/10 rounded-lg status-glow">
                <Layers className="w-5 h-5 text-critical" />
              </div>
            </div>
          </Card>
        </div>

        {/* India Map with DWLR Stations */}
        <IndiaMap 
          stations={dwlrStations}
          onStationClick={(station) => setSelectedStation(station.id)}
          selectedStation={selectedStation}
        />

        {/* Enhanced Chart with DWLR Data */}
        <SimpleChart 
          data={chartData}
          title={`Recent Trend - ${sampleStation.name} (${sampleStation.dwlrId})`}
          height={200}
          showThresholds={true}
          safeThreshold={sampleStation.safeThreshold}
          criticalThreshold={sampleStation.criticalThreshold}
          showTemperature={true}
        />

        {/* Search */}
        <div className="relative group">
          <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 text-muted-foreground w-4 h-4 group-hover:text-primary transition-colors" />
          <Input
            placeholder="Search stations or districts..."
            value={searchQuery}
            onChange={(e) => setSearchQuery(e.target.value)}
            className="pl-10 transition-all duration-300 focus:ring-2 focus:ring-primary/20 focus:border-primary/50 hover:border-primary/30"
          />
        </div>

        {/* Station List */}
        <div className="space-y-4">
          <div className="flex items-center justify-between">
            <h2 className="text-lg font-semibold">Recent Stations</h2>
              <Button variant="outline" size="sm" className="btn-hover-slide">
                <a href="/stations" className="block w-full">View All</a>
              </Button>
          </div>
          
          <div className="grid gap-4">
            {filteredStations.slice(0, 6).map((station) => (
              <StationCard
                key={station.id}
                id={station.id}
                dwlrId={station.dwlrId}
                name={station.name}
                location={`${station.district}, ${station.state}`}
                currentLevel={station.currentLevel}
                status={station.status}
                lastUpdated={new Date(station.lastUpdated).toLocaleDateString('en-IN', {
                  hour: '2-digit',
                  minute: '2-digit'
                })}
                aquiferType={station.aquiferType}
                trend={station.trend}
                rechargeStatus={station.rechargeStatus}
                waterQuality={station.waterQuality.quality}
              />
            ))}
          </div>
        </div>
      </div>
    </div>
  );
};