import { Card } from "@/components/ui/card";
import { Badge } from "@/components/ui/badge";

interface DataPoint {
  time: string;
  level: number;
  temperature?: number;
  batteryLevel?: number;
}

interface SimpleChartProps {
  data: DataPoint[];
  title: string;
  height?: number;
  showThresholds?: boolean;
  safeThreshold?: number;
  criticalThreshold?: number;
  showTemperature?: boolean;
}

export const SimpleChart = ({ 
  data, 
  title, 
  height = 200, 
  showThresholds = false,
  safeThreshold,
  criticalThreshold,
  showTemperature = false
}: SimpleChartProps) => {
  const maxLevel = Math.max(...data.map(d => d.level));
  const minLevel = Math.min(...data.map(d => d.level));
  const range = maxLevel - minLevel || 1;

  const getPointPosition = (index: number, level: number) => {
    const x = (index / (data.length - 1)) * 100;
    const y = ((maxLevel - level) / range) * 80 + 10; // 10% padding
    return { x, y };
  };

  const pathData = data.map((point, index) => {
    const { x, y } = getPointPosition(index, point.level);
    return `${index === 0 ? 'M' : 'L'} ${x} ${y}`;
  }).join(' ');

  const getCurrentStatus = () => {
    if (!data.length) return 'default';
    const currentLevel = data[data.length - 1].level;
    if (currentLevel < 2) return 'critical';
    if (currentLevel < 5) return 'warning';
    return 'success';
  };

  const getStatusColor = (status: string) => {
    switch (status) {
      case 'success': return '#22c55e';
      case 'warning': return '#f59e0b';
      case 'critical': return '#ef4444';
      default: return '#3b82f6';
    }
  };

  const status = getCurrentStatus();
  const strokeColor = getStatusColor(status);

  const avgTemp = showTemperature && data.some(d => d.temperature) 
    ? data.reduce((sum, d) => sum + (d.temperature || 0), 0) / data.length 
    : null;

  const getSafeThresholdY = () => {
    if (!safeThreshold) return null;
    return ((maxLevel - safeThreshold) / range) * 80 + 10;
  };

  const getCriticalThresholdY = () => {
    if (!criticalThreshold) return null;
    return ((maxLevel - criticalThreshold) / range) * 80 + 10;
  };

  return (
    <Card className="p-4 shadow-soft card-hover-glow">
      {title && (
        <div className="flex items-center justify-between mb-4">
          <h3 className="text-lg font-semibold gradient-text">{title}</h3>
          <div className="flex gap-2">
            <Badge variant="outline" className={`text-xs transition-all duration-300 hover:scale-105 ${strokeColor === '#22c55e' ? 'bg-success/10 text-success hover:bg-success/20' : strokeColor === '#f59e0b' ? 'bg-warning/10 text-warning hover:bg-warning/20' : 'bg-critical/10 text-critical hover:bg-critical/20'}`}>
              {status === 'success' ? 'Safe' : status === 'warning' ? 'Moderate' : 'Critical'}
            </Badge>
            {avgTemp && (
              <Badge variant="outline" className="text-xs bg-accent/10 text-accent hover:bg-accent/20 transition-all duration-300 hover:scale-105">
                {avgTemp.toFixed(1)}°C
              </Badge>
            )}
          </div>
        </div>
      )}
      
      <div className="relative chart-container" style={{ height }}>
        <svg className="w-full h-full" viewBox="0 0 100 100" preserveAspectRatio="none">
          {/* Grid lines */}
          <defs>
            <pattern id="grid" width="20" height="20" patternUnits="userSpaceOnUse">
              <path d="M 20 0 L 0 0 0 20" fill="none" stroke="rgb(229, 231, 235)" strokeWidth="0.3" className="chart-grid"/>
            </pattern>
            <linearGradient id="areaGradient" x1="0%" y1="0%" x2="0%" y2="100%">
              <stop offset="0%" stopColor={strokeColor} stopOpacity="0.3"/>
              <stop offset="50%" stopColor={strokeColor} stopOpacity="0.15"/>
              <stop offset="100%" stopColor={strokeColor} stopOpacity="0.05"/>
            </linearGradient>
            <filter id="glow">
              <feGaussianBlur stdDeviation="1" result="coloredBlur"/>
              <feMerge> 
                <feMergeNode in="coloredBlur"/>
                <feMergeNode in="SourceGraphic"/>
              </feMerge>
            </filter>
          </defs>
          <rect width="100" height="100" fill="url(#grid)" opacity="0.4" />
          
          {/* Threshold lines */}
          {showThresholds && safeThreshold && (
            <line
              x1="0"
              y1={getSafeThresholdY()}
              x2="100"
              y2={getSafeThresholdY()}
              stroke="rgb(34, 197, 94)"
              strokeWidth="1"
              strokeDasharray="3,3"
              opacity="0.7"
            />
          )}
          
          {showThresholds && criticalThreshold && (
            <line
              x1="0"
              y1={getCriticalThresholdY()}
              x2="100"
              y2={getCriticalThresholdY()}
              stroke="rgb(239, 68, 68)"
              strokeWidth="1"
              strokeDasharray="3,3"
              opacity="0.7"
            />
          )}
          
          {/* Area fill */}
          <path
            d={`${pathData} L 100 90 L 0 90 Z`}
            fill="url(#areaGradient)"
            opacity="0.4"
          />
          
          {/* Chart line */}
          <path
            d={pathData}
            fill="none"
            stroke={strokeColor}
            strokeWidth="3"
            strokeLinecap="round"
            strokeLinejoin="round"
            className="chart-line"
            filter="url(#glow)"
          />
          
          {/* Data points */}
          {data.map((point, index) => {
            const { x, y } = getPointPosition(index, point.level);
            return (
              <g key={index} className="chart-point">
                <circle
                  cx={x}
                  cy={y}
                  r="4"
                  fill="white"
                  stroke={strokeColor}
                  strokeWidth="2.5"
                  className="hover:r-5 transition-all duration-200 cursor-pointer"
                  filter="url(#glow)"
                />
                <circle
                  cx={x}
                  cy={y}
                  r="2"
                  fill={strokeColor}
                  className="hover:r-2.5 transition-all duration-200"
                />
                {/* Hover tooltip area */}
                <circle
                  cx={x}
                  cy={y}
                  r="8"
                  fill="transparent"
                  className="cursor-pointer"
                />
              </g>
            );
          })}
        </svg>
        
        {/* Y-axis labels */}
        <div className="absolute -left-2 top-0 h-full flex flex-col justify-between text-xs text-muted-foreground font-medium">
          <span className="bg-background px-1 rounded">{maxLevel.toFixed(1)}m</span>
          <span className="bg-background px-1 rounded">{((maxLevel + minLevel) / 2).toFixed(1)}m</span>
          <span className="bg-background px-1 rounded">{minLevel.toFixed(1)}m</span>
        </div>

        {/* Threshold labels */}
        {showThresholds && safeThreshold && (
          <div 
            className="absolute right-2 text-xs text-success font-medium bg-background px-1 rounded"
            style={{ top: `${getSafeThresholdY()}%`, transform: 'translateY(-50%)' }}
          >
            Safe: {safeThreshold}m
          </div>
        )}
        
        {showThresholds && criticalThreshold && (
          <div 
            className="absolute right-2 text-xs text-critical font-medium bg-background px-1 rounded"
            style={{ top: `${getCriticalThresholdY()}%`, transform: 'translateY(-50%)' }}
          >
            Critical: {criticalThreshold}m
          </div>
        )}
      </div>
      
      {/* X-axis labels */}
      <div className="flex justify-between mt-3 text-xs text-muted-foreground font-medium">
        {data.length > 0 && (
          <>
            <span className="bg-accent/10 px-2 py-1 rounded">{data[0].time}</span>
            <span className="bg-accent/10 px-2 py-1 rounded">{data[Math.floor(data.length / 2)]?.time}</span>
            <span className="bg-accent/10 px-2 py-1 rounded">{data[data.length - 1].time}</span>
          </>
        )}
      </div>

      {/* Chart statistics */}
      <div className="mt-4 grid grid-cols-3 gap-4 pt-3 border-t border-border/50">
        <div className="text-center">
          <p className="text-xs text-muted-foreground">Current</p>
          <p className="text-sm font-bold text-foreground">{data[data.length - 1]?.level.toFixed(2)}m</p>
        </div>
        <div className="text-center">
          <p className="text-xs text-muted-foreground">Max</p>
          <p className="text-sm font-bold text-foreground">{maxLevel.toFixed(2)}m</p>
        </div>
        <div className="text-center">
          <p className="text-xs text-muted-foreground">Min</p>
          <p className="text-sm font-bold text-foreground">{minLevel.toFixed(2)}m</p>
        </div>
      </div>
    </Card>
  );
};