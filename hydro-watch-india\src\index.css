@tailwind base;
@tailwind components;
@tailwind utilities;

/* Definition of the design system. All colors, gradients, fonts, etc should be defined here. 
All colors MUST be HSL.
*/

@layer base {
  :root {
    /* Core theme - Water & Earth inspired */
    --background: 210 20% 98%;
    --foreground: 210 15% 20%;

    --card: 0 0% 100%;
    --card-foreground: 210 15% 20%;

    --popover: 0 0% 100%;
    --popover-foreground: 210 15% 20%;

    /* Primary - Deep water blue */
    --primary: 210 85% 45%;
    --primary-foreground: 0 0% 100%;
    --primary-hover: 210 85% 40%;

    /* Secondary - Earth tone */
    --secondary: 25 25% 88%;
    --secondary-foreground: 210 15% 20%;

    --muted: 210 20% 95%;
    --muted-foreground: 210 10% 45%;

    --accent: 195 85% 92%;
    --accent-foreground: 195 85% 25%;

    /* Status colors */
    --success: 142 85% 45%;
    --success-foreground: 0 0% 100%;
    --warning: 45 85% 55%;
    --warning-foreground: 0 0% 100%;
    --destructive: 0 85% 55%;
    --destructive-foreground: 0 0% 100%;
    --critical: 10 85% 50%;
    --critical-foreground: 0 0% 100%;

    --border: 210 15% 88%;
    --input: 210 15% 88%;
    --ring: 210 85% 45%;

    /* Gradients */
    --gradient-primary: linear-gradient(135deg, hsl(210 85% 45%), hsl(195 85% 50%));
    --gradient-success: linear-gradient(135deg, hsl(142 75% 45%), hsl(120 85% 50%));
    --gradient-warning: linear-gradient(135deg, hsl(45 85% 55%), hsl(35 85% 60%));
    --gradient-critical: linear-gradient(135deg, hsl(0 85% 55%), hsl(10 85% 50%));
    
    /* Shadows */
    --shadow-soft: 0 2px 8px hsl(210 15% 20% / 0.08);
    --shadow-medium: 0 4px 16px hsl(210 15% 20% / 0.12);
    --shadow-strong: 0 8px 32px hsl(210 15% 20% / 0.16);

    --radius: 0.75rem;

    --sidebar-background: 0 0% 98%;

    --sidebar-foreground: 240 5.3% 26.1%;

    --sidebar-primary: 240 5.9% 10%;

    --sidebar-primary-foreground: 0 0% 98%;

    --sidebar-accent: 240 4.8% 95.9%;

    --sidebar-accent-foreground: 240 5.9% 10%;

    --sidebar-border: 220 13% 91%;

    --sidebar-ring: 217.2 91.2% 59.8%;
  }

  .dark {
    --background: 222.2 84% 4.9%;
    --foreground: 210 40% 98%;

    --card: 222.2 84% 4.9%;
    --card-foreground: 210 40% 98%;

    --popover: 222.2 84% 4.9%;
    --popover-foreground: 210 40% 98%;

    --primary: 210 40% 98%;
    --primary-foreground: 222.2 47.4% 11.2%;

    --secondary: 217.2 32.6% 17.5%;
    --secondary-foreground: 210 40% 98%;

    --muted: 217.2 32.6% 17.5%;
    --muted-foreground: 215 20.2% 65.1%;

    --accent: 217.2 32.6% 17.5%;
    --accent-foreground: 210 40% 98%;

    --destructive: 0 62.8% 30.6%;
    --destructive-foreground: 210 40% 98%;

    --border: 217.2 32.6% 17.5%;
    --input: 217.2 32.6% 17.5%;
    --ring: 212.7 26.8% 83.9%;
    --sidebar-background: 240 5.9% 10%;
    --sidebar-foreground: 240 4.8% 95.9%;
    --sidebar-primary: 224.3 76.3% 48%;
    --sidebar-primary-foreground: 0 0% 100%;
    --sidebar-accent: 240 3.7% 15.9%;
    --sidebar-accent-foreground: 240 4.8% 95.9%;
    --sidebar-border: 240 3.7% 15.9%;
    --sidebar-ring: 217.2 91.2% 59.8%;
  }
}

@layer base {
  * {
    @apply border-border;
  }

  body {
    @apply bg-background text-foreground;
    font-feature-settings: "rlig" 1, "calt" 1;
  }
}

@layer components {
  /* Enhanced button hover effects */
  .btn-hover-lift {
    @apply transition-all duration-300 ease-out transform hover:scale-105 hover:shadow-lg;
  }
  
  .btn-hover-glow {
    @apply transition-all duration-300 ease-out hover:shadow-lg hover:shadow-primary/25;
  }
  
  .btn-hover-slide {
    @apply transition-all duration-300 ease-out hover:translate-x-1;
  }
  
  .btn-hover-bounce {
    @apply transition-all duration-300 ease-out hover:animate-pulse;
  }

  /* Enhanced card animations */
  .card-hover {
    @apply transition-all duration-300 ease-out hover:shadow-medium hover:-translate-y-1;
  }
  
  .card-hover-glow {
    @apply transition-all duration-300 ease-out hover:shadow-lg hover:shadow-primary/10;
  }

  /* Gradient text effects */
  .gradient-text {
    @apply bg-gradient-to-r from-primary to-accent bg-clip-text text-transparent;
  }
  
  .gradient-text-success {
    @apply bg-gradient-to-r from-success to-green-400 bg-clip-text text-transparent;
  }
  
  .gradient-text-warning {
    @apply bg-gradient-to-r from-warning to-orange-400 bg-clip-text text-transparent;
  }
  
  .gradient-text-critical {
    @apply bg-gradient-to-r from-critical to-red-400 bg-clip-text text-transparent;
  }

  /* Enhanced status indicators */
  .status-pulse {
    @apply animate-pulse;
  }
  
  .status-glow {
    @apply shadow-lg shadow-current/25;
  }

  /* Map container styles */
  .map-container {
    @apply relative overflow-hidden rounded-lg border-2 border-dashed border-border/50;
  }
  
  .map-overlay {
    @apply absolute inset-0 bg-gradient-to-br from-primary/5 to-accent/10 flex items-center justify-center;
  }

  /* Enhanced chart styles */
  .chart-container {
    @apply relative overflow-hidden rounded-lg;
  }
  
  .chart-grid {
    @apply opacity-30;
  }
  
  .chart-line {
    @apply transition-all duration-300 ease-out;
  }
  
  .chart-point {
    @apply transition-all duration-200 ease-out hover:scale-125 cursor-pointer;
  }

  /* Loading animations */
  .loading-shimmer {
    @apply animate-pulse bg-gradient-to-r from-muted via-muted/50 to-muted;
  }
  
  .loading-dots {
    @apply inline-flex space-x-1;
  }
  
  .loading-dots > div {
    @apply w-2 h-2 bg-current rounded-full animate-bounce;
  }
  
  .loading-dots > div:nth-child(2) {
    animation-delay: 0.1s;
  }
  
  .loading-dots > div:nth-child(3) {
    animation-delay: 0.2s;
  }
}