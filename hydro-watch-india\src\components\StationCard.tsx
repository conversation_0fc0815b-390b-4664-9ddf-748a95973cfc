import { Card } from "@/components/ui/card";
import { Badge } from "@/components/ui/badge";
import { MapPin, Activity, AlertTriangle, Database, TrendingUp, TrendingDown, Minus, Layers, Waves, Mountain, Beaker, Droplets, Zap } from "lucide-react";
import { useNavigate } from "react-router-dom";

interface StationCardProps {
  id: string;
  dwlrId: string;
  name: string;
  location: string;
  currentLevel: number;
  status: 'safe' | 'moderate' | 'critical' | 'dry';
  lastUpdated: string;
  aquiferType: 'alluvial' | 'hard_rock' | 'coastal' | 'volcanic';
  trend: 'rising' | 'stable' | 'declining';
  rechargeStatus: 'active' | 'moderate' | 'poor' | 'none';
  waterQuality: 'excellent' | 'good' | 'acceptable' | 'poor';
}

export const StationCard = ({ 
  id,
  dwlrId,
  name, 
  location, 
  currentLevel, 
  status, 
  lastUpdated,
  aquiferType,
  trend,
  rechargeStatus,
  waterQuality
}: StationCardProps) => {
  const navigate = useNavigate();
  const getStatusColor = (status: string) => {
    switch (status) {
      case 'safe': return 'bg-gradient-success';
      case 'moderate': return 'bg-gradient-warning';
      case 'critical': return 'bg-gradient-critical';
      case 'dry': return 'bg-gradient-critical';
      default: return 'bg-gradient-primary';
    }
  };

  const getStatusText = (status: string) => {
    switch (status) {
      case 'safe': return 'Safe Level';
      case 'moderate': return 'Moderate Risk';
      case 'critical': return 'Critical';
      case 'dry': return 'Dry Well';
      default: return 'Unknown';
    }
  };

  const getAquiferIcon = (type: string) => {
    switch (type) {
      case 'alluvial': return <Layers className="w-3 h-3" />;
      case 'hard_rock': return <Mountain className="w-3 h-3" />;
      case 'coastal': return <Waves className="w-3 h-3" />;
      case 'volcanic': return <Activity className="w-3 h-3" />;
      default: return <Layers className="w-3 h-3" />;
    }
  };

  const getTrendIcon = (trend: string) => {
    switch (trend) {
      case 'rising': return <TrendingUp className="w-3 h-3 text-success" />;
      case 'declining': return <TrendingDown className="w-3 h-3 text-critical" />;
      default: return <Minus className="w-3 h-3 text-muted-foreground" />;
    }
  };

  const getRechargeColor = (status: string) => {
    switch (status) {
      case 'active': return 'text-success';
      case 'moderate': return 'text-warning';
      case 'poor': return 'text-warning';
      case 'none': return 'text-critical';
      default: return 'text-muted-foreground';
    }
  };

  const getQualityColor = (quality: string) => {
    switch (quality) {
      case 'excellent': return 'text-success';
      case 'good': return 'text-success';
      case 'acceptable': return 'text-warning';
      case 'poor': return 'text-critical';
      default: return 'text-muted-foreground';
    }
  };

  return (
    <Card 
      className="p-4 shadow-soft card-hover cursor-pointer group"
      onClick={() => navigate(`/station/${id}`)}
    >
      {/* Header with station info and status */}
      <div className="flex items-start justify-between mb-3">
        <div className="flex-1">
          <div className="flex items-center gap-2 mb-1">
            <h3 className="font-semibold text-foreground text-lg group-hover:gradient-text transition-all duration-300">{name}</h3>
            <Badge variant="outline" className="text-xs bg-primary/10 text-primary group-hover:bg-primary/20 transition-colors">
              {dwlrId}
            </Badge>
          </div>
          <div className="flex items-center text-muted-foreground text-sm mb-2">
            <MapPin className="w-4 h-4 mr-1" />
            {location}
          </div>
          <div className="flex items-center gap-3 text-xs text-muted-foreground">
            <div className="flex items-center">
              {getAquiferIcon(aquiferType)}
              <span className="ml-1 capitalize">{aquiferType.replace('_', ' ')}</span>
            </div>
            <div className="flex items-center">
              {getTrendIcon(trend)}
              <span className="ml-1 capitalize">{trend}</span>
            </div>
          </div>
        </div>
        <div className={`px-3 py-1 rounded-full text-sm font-medium text-white ${getStatusColor(status)} group-hover:scale-105 transition-transform duration-300`}>
          {getStatusText(status)}
        </div>
      </div>
      
      {/* Main data display */}
      <div className="flex items-center justify-between mb-3">
        <div>
          <div className="flex items-center text-primary group-hover:text-primary/80 transition-colors">
            <Droplets className="w-5 h-5 mr-2 group-hover:animate-pulse" />
            <span className="text-2xl font-bold group-hover:gradient-text transition-all duration-300">{currentLevel}m</span>
          </div>
          <p className="text-xs text-muted-foreground">Water Level</p>
        </div>
        
        <div className="text-right">
          <p className="text-xs text-muted-foreground">Last Updated</p>
          <p className="text-sm font-medium">{lastUpdated}</p>
        </div>
      </div>

      {/* Additional indicators */}
      <div className="flex items-center justify-between text-xs">
        <div className="flex items-center">
          <Zap className="w-3 h-3 mr-1" />
          <span className={getRechargeColor(rechargeStatus)}>
            {rechargeStatus} recharge
          </span>
        </div>
        <div className="flex items-center">
          <Beaker className="w-3 h-3 mr-1" />
          <span className={getQualityColor(waterQuality)}>
            {waterQuality} quality
          </span>
        </div>
      </div>
      
      {/* Alert banners */}
      {(status === 'critical' || status === 'dry') && (
        <div className="mt-3 p-2 bg-critical/10 rounded-lg flex items-center">
          <AlertTriangle className="w-4 h-4 text-critical mr-2" />
          <span className="text-sm text-critical">
            {status === 'dry' ? 'Alert: Well is dry' : 'Alert: Below safe threshold'}
          </span>
        </div>
      )}
      
      {trend === 'declining' && (
        <div className="mt-2 p-2 bg-warning/10 rounded-lg flex items-center">
          <TrendingDown className="w-4 h-4 text-warning mr-2" />
          <span className="text-sm text-warning">Declining water level trend</span>
        </div>
      )}
    </Card>
  );
};