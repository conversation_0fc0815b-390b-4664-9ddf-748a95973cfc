import { Card } from "@/components/ui/card";
import { Badge } from "@/components/ui/badge";
import { Progress } from "@/components/ui/progress";
import { 
  Mountain, 
  Waves, 
  Layers, 
  TrendingDown, 
  TrendingUp, 
  Minus,
  Calendar,
  Activity
} from "lucide-react";

interface AquiferInfoProps {
  aquiferType: 'alluvial' | 'hard_rock' | 'coastal' | 'volcanic';
  aquiferDepth: number;
  wellDepth: number;
  depthToWater: number;
  staticWaterLevel: number;
  trend: 'rising' | 'stable' | 'declining';
  monthlyChange: number;
  yearlyChange: number;
  rechargeStatus: 'active' | 'moderate' | 'poor' | 'none';
  installationDate: string;
}

export const AquiferInfoCard = ({
  aquiferType,
  aquiferDepth,
  wellDepth,
  depthToWater,
  staticWaterLevel,
  trend,
  monthlyChange,
  yearlyChange,
  rechargeStatus,
  installationDate
}: AquiferInfoProps) => {
  
  const getAquiferIcon = (type: string) => {
    switch (type) {
      case 'alluvial': return <Layers className="w-4 h-4" />;
      case 'hard_rock': return <Mountain className="w-4 h-4" />;
      case 'coastal': return <Waves className="w-4 h-4" />;
      case 'volcanic': return <Activity className="w-4 h-4" />;
      default: return <Layers className="w-4 h-4" />;
    }
  };

  const getAquiferDescription = (type: string) => {
    switch (type) {
      case 'alluvial': return 'Sedimentary deposits';
      case 'hard_rock': return 'Crystalline formations';
      case 'coastal': return 'Coastal sediments';
      case 'volcanic': return 'Volcanic formations';
      default: return 'Unknown formation';
    }
  };

  const getTrendIcon = (trend: string) => {
    switch (trend) {
      case 'rising': return <TrendingUp className="w-4 h-4 text-success" />;
      case 'declining': return <TrendingDown className="w-4 h-4 text-critical" />;
      default: return <Minus className="w-4 h-4 text-muted-foreground" />;
    }
  };

  const getRechargeColor = (status: string) => {
    switch (status) {
      case 'active': return 'success';
      case 'moderate': return 'warning'; 
      case 'poor': return 'warning';
      case 'none': return 'critical';
      default: return 'default';
    }
  };

  const getRechargeScore = (status: string) => {
    switch (status) {
      case 'active': return 85;
      case 'moderate': return 60;
      case 'poor': return 35;
      case 'none': return 10;
      default: return 0;
    }
  };

  const waterUtilization = ((depthToWater / wellDepth) * 100);
  const aquiferUtilization = ((depthToWater / aquiferDepth) * 100);
  const yearsOperating = new Date().getFullYear() - new Date(installationDate).getFullYear();

  return (
    <Card className="p-4 shadow-soft">
      <div className="flex items-center justify-between mb-4">
        <div className="flex items-center">
          {getAquiferIcon(aquiferType)}
          <div className="ml-2">
            <h3 className="text-lg font-semibold">Aquifer Information</h3>
            <p className="text-xs text-muted-foreground capitalize">
              {aquiferType.replace('_', ' ')} - {getAquiferDescription(aquiferType)}
            </p>
          </div>
        </div>
        <Badge variant="outline" className={`bg-${getRechargeColor(rechargeStatus)}/10 text-${getRechargeColor(rechargeStatus)} capitalize`}>
          {rechargeStatus} Recharge
        </Badge>
      </div>

      {/* Depth Information */}
      <div className="space-y-4 mb-6">
        <div className="grid grid-cols-2 gap-4">
          <div className="p-3 bg-accent/5 rounded-lg">
            <p className="text-xs text-muted-foreground">Well Depth</p>
            <p className="text-lg font-bold text-foreground">{wellDepth}m</p>
          </div>
          <div className="p-3 bg-accent/5 rounded-lg">
            <p className="text-xs text-muted-foreground">Aquifer Depth</p>
            <p className="text-lg font-bold text-foreground">{aquiferDepth}m</p>
          </div>
        </div>

        <div className="grid grid-cols-2 gap-4">
          <div className="p-3 bg-accent/5 rounded-lg">
            <p className="text-xs text-muted-foreground">Depth to Water</p>
            <p className="text-lg font-bold text-foreground">{depthToWater}m</p>
          </div>
          <div className="p-3 bg-accent/5 rounded-lg">
            <p className="text-xs text-muted-foreground">Static Water Level</p>
            <p className="text-lg font-bold text-foreground">{staticWaterLevel}m</p>
          </div>
        </div>
      </div>

      {/* Utilization Indicators */}
      <div className="space-y-4 mb-6">
        <div>
          <div className="flex items-center justify-between mb-2">
            <span className="text-sm font-medium">Well Utilization</span>
            <span className="text-sm font-bold">{waterUtilization.toFixed(1)}%</span>
          </div>
          <Progress 
            value={waterUtilization} 
            className={`h-2 ${waterUtilization > 80 ? 'bg-critical/20' : waterUtilization > 60 ? 'bg-warning/20' : 'bg-success/20'}`} 
          />
        </div>

        <div>
          <div className="flex items-center justify-between mb-2">
            <span className="text-sm font-medium">Recharge Activity</span>
            <span className="text-sm font-bold">{getRechargeScore(rechargeStatus)}%</span>
          </div>
          <Progress value={getRechargeScore(rechargeStatus)} className="h-2" />
        </div>
      </div>

      {/* Trend Analysis */}
      <div className="space-y-3 mb-6">
        <div className="flex items-center justify-between p-3 bg-gradient-subtle rounded-lg">
          <div className="flex items-center">
            {getTrendIcon(trend)}
            <div className="ml-2">
              <p className="text-sm font-medium capitalize">{trend} Trend</p>
              <p className="text-xs text-muted-foreground">Water level pattern</p>
            </div>
          </div>
          <div className="text-right">
            <p className="text-sm font-bold">
              {monthlyChange > 0 ? '+' : ''}{monthlyChange}m
            </p>
            <p className="text-xs text-muted-foreground">This month</p>
          </div>
        </div>

        <div className="flex items-center justify-between p-3 bg-gradient-subtle rounded-lg">
          <div className="flex items-center">
            <Calendar className="w-4 h-4 text-primary" />
            <div className="ml-2">
              <p className="text-sm font-medium">Annual Change</p>
              <p className="text-xs text-muted-foreground">12-month trend</p>
            </div>
          </div>
          <div className="text-right">
            <p className="text-sm font-bold">
              {yearlyChange > 0 ? '+' : ''}{yearlyChange}m
            </p>
            <p className="text-xs text-muted-foreground">Past year</p>
          </div>
        </div>
      </div>

      {/* Station Metadata */}
      <div className="pt-3 border-t border-border/50">
        <div className="flex items-center justify-between text-sm">
          <span className="text-muted-foreground">Operating since</span>
          <span className="font-medium">{yearsOperating} years ({new Date(installationDate).getFullYear()})</span>
        </div>
      </div>
    </Card>
  );
};