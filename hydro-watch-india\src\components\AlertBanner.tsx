import { Card } from "@/components/ui/card";
import { But<PERSON> } from "@/components/ui/button";
import { Badge } from "@/components/ui/badge";
import { AlertTriangle, X, Bell } from "lucide-react";
import { useState } from "react";

interface Alert {
  id: string;
  stationName: string;
  message: string;
  level: 'warning' | 'critical';
  timestamp: string;
}

const mockAlerts: Alert[] = [
  {
    id: '1',
    stationName: 'Punjab Station 3',
    message: 'Water level dropped below critical threshold (1.8m)',
    level: 'critical',
    timestamp: '30 min ago'
  },
  {
    id: '2',
    stationName: 'Tamil Nadu Station 6',
    message: 'Water level approaching critical zone (1.2m)',
    level: 'critical',
    timestamp: '15 min ago'
  }
];

export const AlertBanner = () => {
  const [alerts, setAlerts] = useState(mockAlerts);
  const [isExpanded, setIsExpanded] = useState(false);

  const dismissAlert = (alertId: string) => {
    setAlerts(alerts.filter(alert => alert.id !== alertId));
  };

  if (alerts.length === 0) return null;

  const criticalAlerts = alerts.filter(a => a.level === 'critical').length;
  const warningAlerts = alerts.filter(a => a.level === 'warning').length;

  return (
    <div className="p-4">
      <Card className="border-critical/20 bg-gradient-to-r from-critical/5 to-warning/5 shadow-medium">
        <div className="p-4">
          <div className="flex items-center justify-between">
            <div className="flex items-center">
              <AlertTriangle className="w-5 h-5 text-critical mr-3" />
              <div>
                <h3 className="font-semibold text-foreground">Active Alerts</h3>
                <div className="flex items-center gap-2 mt-1">
                  {criticalAlerts > 0 && (
                    <Badge variant="outline" className="bg-critical/10 text-critical text-xs">
                      {criticalAlerts} Critical
                    </Badge>
                  )}
                  {warningAlerts > 0 && (
                    <Badge variant="outline" className="bg-warning/10 text-warning text-xs">
                      {warningAlerts} Warning
                    </Badge>
                  )}
                </div>
              </div>
            </div>
            
            <Button
              variant="ghost"
              size="sm"
              onClick={() => setIsExpanded(!isExpanded)}
            >
              {isExpanded ? 'Hide' : 'View'}
            </Button>
          </div>

          {isExpanded && (
            <div className="mt-4 space-y-3">
              {alerts.map((alert) => (
                <div
                  key={alert.id}
                  className={`p-3 rounded-lg border ${
                    alert.level === 'critical' 
                      ? 'border-critical/20 bg-critical/5' 
                      : 'border-warning/20 bg-warning/5'
                  }`}
                >
                  <div className="flex items-start justify-between">
                    <div className="flex-1">
                      <div className="flex items-center gap-2 mb-1">
                        <Badge 
                          variant="outline" 
                          className={`${
                            alert.level === 'critical' 
                              ? 'bg-critical/10 text-critical' 
                              : 'bg-warning/10 text-warning'
                          } text-xs`}
                        >
                          {alert.level.toUpperCase()}
                        </Badge>
                        <span className="font-medium text-sm">{alert.stationName}</span>
                      </div>
                      <p className="text-sm text-muted-foreground">{alert.message}</p>
                      <p className="text-xs text-muted-foreground mt-1">{alert.timestamp}</p>
                    </div>
                    
                    <Button
                      variant="ghost"
                      size="sm"
                      onClick={() => dismissAlert(alert.id)}
                      className="h-6 w-6 p-0"
                    >
                      <X className="w-3 h-3" />
                    </Button>
                  </div>
                </div>
              ))}
            </div>
          )}
        </div>
      </Card>
    </div>
  );
};