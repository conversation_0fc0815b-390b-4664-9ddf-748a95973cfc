import { useState } from "react";
import { Card } from "@/components/ui/card";
import { Badge } from "@/components/ui/badge";
import { MapPin, AlertTriangle, Droplets, Activity } from "lucide-react";
import { DWLRStation } from "@/data/dwlrData";

interface IndiaMapProps {
  stations: DWLRStation[];
  onStationClick?: (station: DWLRStation) => void;
  selectedStation?: string;
}

export const IndiaMap = ({ stations, onStationClick, selectedStation }: IndiaMapProps) => {
  const [hoveredStation, setHoveredStation] = useState<string | null>(null);

  const getStationColor = (status: string) => {
    switch (status) {
      case 'safe': return '#22c55e';
      case 'moderate': return '#f59e0b';
      case 'critical': return '#ef4444';
      case 'dry': return '#dc2626';
      default: return '#3b82f6';
    }
  };

  const getStationSize = (status: string) => {
    switch (status) {
      case 'safe': return 8;
      case 'moderate': return 10;
      case 'critical': return 12;
      case 'dry': return 12;
      default: return 8;
    }
  };

  // Simplified India map coordinates (approximate)
  const indiaMapPath = "M 50 20 L 80 25 L 95 40 L 90 60 L 85 80 L 70 90 L 50 95 L 30 90 L 15 80 L 10 60 L 15 40 L 30 25 L 50 20 Z";

  // Station positions (approximate coordinates within India)
  const stationPositions: { [key: string]: { x: number; y: number } } = {
    'DWLR_001': { x: 45, y: 35 }, // Rajasthan
    'DWLR_002': { x: 40, y: 50 }, // Gujarat
    'DWLR_003': { x: 50, y: 25 }, // Punjab
    'DWLR_004': { x: 35, y: 60 }, // Maharashtra
    'DWLR_005': { x: 60, y: 75 }, // Tamil Nadu
    'DWLR_006': { x: 45, y: 70 }, // Karnataka
  };

  return (
    <Card className="p-4 shadow-soft card-hover-glow">
      <div className="flex items-center justify-between mb-4">
        <h2 className="text-lg font-semibold gradient-text">DWLR Station Network</h2>
        <div className="flex gap-2">
          <Badge variant="outline" className="bg-success/10 text-success hover:bg-success/20 transition-colors">
            <Droplets className="w-3 h-3 mr-1" />
            Safe
          </Badge>
          <Badge variant="outline" className="bg-warning/10 text-warning hover:bg-warning/20 transition-colors">
            <AlertTriangle className="w-3 h-3 mr-1" />
            Moderate
          </Badge>
          <Badge variant="outline" className="bg-critical/10 text-critical hover:bg-critical/20 transition-colors">
            <Activity className="w-3 h-3 mr-1" />
            Critical
          </Badge>
        </div>
      </div>
      
      <div className="relative h-96 bg-gradient-to-br from-primary/5 to-accent/10 rounded-lg overflow-hidden">
        <svg 
          className="w-full h-full" 
          viewBox="0 0 100 100" 
          preserveAspectRatio="xMidYMid meet"
        >
          {/* India map outline */}
          <path
            d={indiaMapPath}
            fill="none"
            stroke="hsl(var(--primary))"
            strokeWidth="0.5"
            opacity="0.6"
            className="transition-all duration-300"
          />
          
          {/* India map fill */}
          <path
            d={indiaMapPath}
            fill="hsl(var(--primary))"
            fillOpacity="0.1"
            className="transition-all duration-300"
          />
          
          {/* Station markers */}
          {stations.map((station) => {
            const position = stationPositions[station.id];
            if (!position) return null;
            
            const isSelected = selectedStation === station.id;
            const isHovered = hoveredStation === station.id;
            const color = getStationColor(station.status);
            const size = getStationSize(station.status);
            
            return (
              <g key={station.id}>
                {/* Station marker */}
                <circle
                  cx={position.x}
                  cy={position.y}
                  r={isSelected ? size + 2 : size}
                  fill={color}
                  stroke="white"
                  strokeWidth={isSelected ? 3 : 2}
                  className={`transition-all duration-300 cursor-pointer ${
                    isHovered ? 'animate-pulse' : ''
                  }`}
                  onClick={() => onStationClick?.(station)}
                  onMouseEnter={() => setHoveredStation(station.id)}
                  onMouseLeave={() => setHoveredStation(null)}
                  filter="drop-shadow(0 2px 4px rgba(0,0,0,0.2))"
                />
                
                {/* Station icon */}
                <MapPin
                  x={position.x - 1.5}
                  y={position.y - 1.5}
                  width="3"
                  height="3"
                  fill="white"
                  className="pointer-events-none"
                />
                
                {/* Station label (show on hover) */}
                {isHovered && (
                  <g>
                    <rect
                      x={position.x - 8}
                      y={position.y - 12}
                      width="16"
                      height="8"
                      fill="rgba(0,0,0,0.8)"
                      rx="2"
                      className="transition-all duration-200"
                    />
                    <text
                      x={position.x}
                      y={position.y - 6}
                      textAnchor="middle"
                      fill="white"
                      fontSize="2"
                      className="pointer-events-none"
                    >
                      {station.dwlrId}
                    </text>
                  </g>
                )}
              </g>
            );
          })}
          
          {/* State boundaries (simplified) */}
          <g opacity="0.3">
            {/* Rajasthan */}
            <path d="M 30 25 L 60 30 L 65 45 L 50 50 L 30 45 Z" fill="none" stroke="hsl(var(--border))" strokeWidth="0.2" />
            {/* Gujarat */}
            <path d="M 30 45 L 50 50 L 55 65 L 40 70 L 25 60 Z" fill="none" stroke="hsl(var(--border))" strokeWidth="0.2" />
            {/* Punjab */}
            <path d="M 50 20 L 80 25 L 85 40 L 60 35 L 50 20 Z" fill="none" stroke="hsl(var(--border))" strokeWidth="0.2" />
            {/* Maharashtra */}
            <path d="M 25 60 L 40 65 L 45 80 L 30 85 L 20 70 Z" fill="none" stroke="hsl(var(--border))" strokeWidth="0.2" />
            {/* Tamil Nadu */}
            <path d="M 55 70 L 70 75 L 75 90 L 60 95 L 50 85 Z" fill="none" stroke="hsl(var(--border))" strokeWidth="0.2" />
            {/* Karnataka */}
            <path d="M 40 65 L 55 70 L 60 85 L 45 90 L 35 80 Z" fill="none" stroke="hsl(var(--border))" strokeWidth="0.2" />
          </g>
        </svg>
        
        {/* Map overlay with statistics */}
        <div className="absolute top-4 left-4 bg-background/90 backdrop-blur-sm rounded-lg p-3 shadow-lg">
          <div className="text-xs text-muted-foreground mb-2">Network Status</div>
          <div className="space-y-1">
            <div className="flex items-center gap-2">
              <div className="w-2 h-2 rounded-full bg-success"></div>
              <span className="text-xs">Safe: {stations.filter(s => s.status === 'safe').length}</span>
            </div>
            <div className="flex items-center gap-2">
              <div className="w-2 h-2 rounded-full bg-warning"></div>
              <span className="text-xs">Moderate: {stations.filter(s => s.status === 'moderate').length}</span>
            </div>
            <div className="flex items-center gap-2">
              <div className="w-2 h-2 rounded-full bg-critical"></div>
              <span className="text-xs">Critical: {stations.filter(s => s.status === 'critical').length}</span>
            </div>
          </div>
        </div>
        
        {/* Map legend */}
        <div className="absolute bottom-4 right-4 bg-background/90 backdrop-blur-sm rounded-lg p-3 shadow-lg">
          <div className="text-xs text-muted-foreground mb-2">Legend</div>
          <div className="space-y-1">
            <div className="flex items-center gap-2">
              <div className="w-2 h-2 rounded-full bg-primary"></div>
              <span className="text-xs">DWLR Station</span>
            </div>
            <div className="flex items-center gap-2">
              <div className="w-2 h-2 rounded-full bg-success"></div>
              <span className="text-xs">Safe Level</span>
            </div>
            <div className="flex items-center gap-2">
              <div className="w-2 h-2 rounded-full bg-warning"></div>
              <span className="text-xs">Moderate Risk</span>
            </div>
            <div className="flex items-center gap-2">
              <div className="w-2 h-2 rounded-full bg-critical"></div>
              <span className="text-xs">Critical/Dry</span>
            </div>
          </div>
        </div>
      </div>
      
      {/* Station details */}
      {hoveredStation && (
        <div className="mt-4 p-3 bg-muted/50 rounded-lg">
          {(() => {
            const station = stations.find(s => s.id === hoveredStation);
            if (!station) return null;
            return (
              <div className="flex items-center justify-between">
                <div>
                  <h4 className="font-semibold text-sm">{station.name}</h4>
                  <p className="text-xs text-muted-foreground">{station.location}</p>
                </div>
                <div className="text-right">
                  <p className="text-sm font-bold text-primary">{station.currentLevel}m</p>
                  <p className="text-xs text-muted-foreground capitalize">{station.status}</p>
                </div>
              </div>
            );
          })()}
        </div>
      )}
    </Card>
  );
};
