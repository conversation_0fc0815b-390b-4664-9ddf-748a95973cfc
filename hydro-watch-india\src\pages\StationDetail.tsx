import { useState } from "react";
import { useParams, useNavigate } from "react-router-dom";
import { Card } from "@/components/ui/card";
import { Button } from "@/components/ui/button";
import { Badge } from "@/components/ui/badge";
import { SimpleChart } from "@/components/SimpleChart";
import { WaterQualityCard } from "@/components/WaterQualityCard";
import { AquiferInfoCard } from "@/components/AquiferInfoCard";
import { 
  ArrowLeft, 
  MapPin, 
  Activity, 
  AlertTriangle, 
  Download,
  Share,
  Bell,
  Droplets,
  TrendingUp,
  TrendingDown,
  Minus,
  Database,
  Thermometer,
  Battery
} from "lucide-react";
import { dwlrStations, generateHourlyData, generateWeeklyData } from "@/data/dwlrData";

export const StationDetail = () => {
  const { id } = useParams<{ id: string }>();
  const navigate = useNavigate();
  const [viewMode, setViewMode] = useState<'24h' | 'week'>('24h');
  
  const station = dwlrStations.find(s => s.id === id);
  
  if (!station) {
    return (
      <div className="min-h-screen bg-background flex items-center justify-center">
        <div className="text-center">
          <h2 className="text-2xl font-bold mb-2">Station Not Found</h2>
          <Button onClick={() => navigate('/dashboard')}>
            Back to Dashboard
          </Button>
        </div>
      </div>
    );
  }

  // Generate chart data
  const hourlyData = generateHourlyData(station).map(reading => ({
    time: new Date(reading.timestamp).toTimeString().slice(0, 5),
    level: reading.level,
    temperature: reading.temperature,
    batteryLevel: reading.batteryLevel
  }));

  const weeklyData = generateWeeklyData(station).map(reading => ({
    time: new Date(reading.timestamp).toLocaleDateString('en-IN', { weekday: 'short' }),
    level: reading.level,
    temperature: reading.temperature,
    batteryLevel: reading.batteryLevel
  }));

  const chartData = viewMode === '24h' ? hourlyData : weeklyData;

  const getStatusColor = (status: string) => {
    switch (status) {
      case 'safe': return 'success';
      case 'moderate': return 'warning';
      case 'critical': return 'critical';
      case 'dry': return 'critical';
      default: return 'default';
    }
  };

  const getRechargeColor = (status: string) => {
    switch (status) {
      case 'active': return 'success';
      case 'moderate': return 'warning';
      case 'poor': return 'warning';
      case 'none': return 'critical';
      default: return 'default';
    }
  };

  const getTrendIcon = (trend: string) => {
    switch (trend) {
      case 'rising': return <TrendingUp className="w-4 h-4 text-success" />;
      case 'declining': return <TrendingDown className="w-4 h-4 text-critical" />;
      default: return <Minus className="w-4 h-4 text-muted-foreground" />;
    }
  };

  return (
    <div className="min-h-screen bg-background">
      {/* Enhanced Header */}
      <div className="bg-gradient-primary text-white p-4">
        <div className="flex items-center justify-between mb-4">
          <div className="flex items-center">
            <Button
              variant="outline"
              size="sm"
              className="text-white border-white/30 mr-3"
              onClick={() => navigate('/dashboard')}
            >
              <ArrowLeft className="w-4 h-4" />
            </Button>
            <div>
              <div className="flex items-center gap-2 mb-1">
                <h1 className="text-xl font-bold">{station.name}</h1>
                <Badge variant="outline" className="text-white border-white/30 text-xs">
                  {station.dwlrId}
                </Badge>
              </div>
              <p className="text-sm opacity-90">{station.district}, {station.state}</p>
              <p className="text-xs opacity-75">{station.coordinates.lat.toFixed(4)}°N, {station.coordinates.lng.toFixed(4)}°E</p>
            </div>
          </div>
          
          <div className="flex gap-2">
            <Button variant="outline" size="sm" className="text-white border-white/30">
              <Bell className="w-4 h-4" />
            </Button>
            <Button variant="outline" size="sm" className="text-white border-white/30">
              <Share className="w-4 h-4" />
            </Button>
          </div>
        </div>
      </div>

      <div className="p-4 space-y-6">
        {/* Enhanced Current Status */}
        <Card className="p-4 shadow-soft">
          <div className="flex items-center justify-between mb-4">
            <h2 className="text-lg font-semibold">Current Status</h2>
            <Badge variant="outline" className={`bg-${getStatusColor(station.status)}/10 text-${getStatusColor(station.status)}`}>
              {station.status.toUpperCase()}
            </Badge>
          </div>

          <div className="grid grid-cols-2 md:grid-cols-4 gap-4">
            <div className="text-center">
              <div className="flex items-center justify-center text-primary mb-2">
                <Droplets className="w-6 h-6 mr-2" />
                <span className="text-2xl font-bold">{station.currentLevel}m</span>
              </div>
              <p className="text-sm text-muted-foreground">Current Level</p>
              <p className="text-xs text-muted-foreground">SWL: {station.staticWaterLevel}m</p>
            </div>

            <div className="text-center">
              <div className="flex items-center justify-center text-muted-foreground mb-2">
                {getTrendIcon(station.trend)}
                <span className="text-lg font-semibold ml-2 capitalize">{station.trend}</span>
              </div>
              <p className="text-sm text-muted-foreground">Trend</p>
              <p className="text-xs text-muted-foreground">
                {station.monthlyChange > 0 ? '+' : ''}{station.monthlyChange}m/month
              </p>
            </div>

            <div className="text-center">
              <div className={`text-${getRechargeColor(station.rechargeStatus)} mb-2`}>
                <span className="text-lg font-semibold capitalize">{station.rechargeStatus}</span>
              </div>
              <p className="text-sm text-muted-foreground">Recharge</p>
              <p className="text-xs text-muted-foreground">Aquifer activity</p>
            </div>

            <div className="text-center">
              <div className="text-muted-foreground mb-2">
                <span className="text-sm">
                  {new Date(station.lastUpdated).toLocaleDateString('en-IN', {
                    hour: '2-digit',
                    minute: '2-digit'
                  })}
                </span>
              </div>
              <p className="text-sm text-muted-foreground">Last Update</p>
              <p className="text-xs text-muted-foreground">Auto-sync {station.dataFrequency}</p>
            </div>
          </div>

          {/* Additional metrics */}
          <div className="mt-4 grid grid-cols-3 gap-4 pt-4 border-t border-border/50">
            <div className="text-center">
              <p className="text-xs text-muted-foreground">Depth to Water</p>
              <p className="text-sm font-bold">{station.depthToWater}m</p>
            </div>
            <div className="text-center">
              <p className="text-xs text-muted-foreground">Well Depth</p>
              <p className="text-sm font-bold">{station.wellDepth}m</p>
            </div>
            <div className="text-center">
              <p className="text-xs text-muted-foreground">Aquifer</p>
              <p className="text-sm font-bold capitalize">{station.aquiferType.replace('_', ' ')}</p>
            </div>
          </div>
        </Card>

        {/* Alert Banner */}
        {station.currentLevel < station.safeThreshold && (
          <Card className="p-4 border-warning bg-warning/5">
            <div className="flex items-center">
              <AlertTriangle className="w-5 h-5 text-warning mr-3" />
              <div>
                <p className="font-medium text-warning">Water Level Below Safe Threshold</p>
                <p className="text-sm text-muted-foreground">
                  Current: {station.currentLevel}m | Safe: {station.safeThreshold}m | Critical: {station.criticalThreshold}m
                </p>
              </div>
            </div>
          </Card>
        )}

        {/* Enhanced Chart Section */}
        <Card className="p-4 shadow-soft">
          <div className="flex items-center justify-between mb-4">
            <h2 className="text-lg font-semibold">Water Level Trend</h2>
            <div className="flex gap-2">
              <Button
                variant={viewMode === '24h' ? 'default' : 'outline'}
                size="sm"
                onClick={() => setViewMode('24h')}
              >
                24 Hours
              </Button>
              <Button
                variant={viewMode === 'week' ? 'default' : 'outline'}
                size="sm"
                onClick={() => setViewMode('week')}
              >
                Week
              </Button>
            </div>
          </div>

          <SimpleChart
            data={chartData}
            title=""
            height={250}
            showThresholds={true}
            safeThreshold={station.safeThreshold}
            criticalThreshold={station.criticalThreshold}
            showTemperature={true}
          />
        </Card>

        {/* Water Quality Card */}
        <WaterQualityCard data={station.waterQuality} />

        {/* Aquifer Information Card */}
        <AquiferInfoCard
          aquiferType={station.aquiferType}
          aquiferDepth={station.aquiferDepth}
          wellDepth={station.wellDepth}
          depthToWater={station.depthToWater}
          staticWaterLevel={station.staticWaterLevel}
          trend={station.trend}
          monthlyChange={station.monthlyChange}
          yearlyChange={station.yearlyChange}
          rechargeStatus={station.rechargeStatus}
          installationDate={station.installationDate}
        />

        {/* Enhanced Actions */}
        <div className="grid grid-cols-2 gap-4">
          <Button className="w-full" variant="outline">
            <Download className="w-4 h-4 mr-2" />
            Generate Report
          </Button>
          
          <Button className="w-full">
            <Bell className="w-4 h-4 mr-2" />
            Set Alert
          </Button>
        </div>

        {/* Enhanced Map with Station Details */}
        <Card className="p-4 shadow-soft">
          <h3 className="text-lg font-semibold mb-4">Station Location & Details</h3>
          <div className="h-48 bg-gradient-to-br from-primary/5 to-accent/10 rounded-lg flex items-center justify-center border-2 border-dashed border-border mb-4">
            <div className="text-center text-muted-foreground">
              <MapPin className="w-12 h-12 mx-auto mb-2 opacity-50" />
              <p className="text-sm">Interactive station map</p>
              <p className="text-xs mt-1">{station.coordinates.lat.toFixed(4)}°N, {station.coordinates.lng.toFixed(4)}°E</p>
            </div>
          </div>
          
          {/* Station metadata */}
          <div className="grid grid-cols-2 gap-4 text-sm">
            <div className="p-3 bg-accent/5 rounded-lg">
              <p className="text-muted-foreground">Installation Date</p>
              <p className="font-medium">{new Date(station.installationDate).toLocaleDateString('en-IN')}</p>
            </div>
            <div className="p-3 bg-accent/5 rounded-lg">
              <p className="text-muted-foreground">Data Frequency</p>
              <p className="font-medium capitalize">{station.dataFrequency}</p>
            </div>
          </div>
        </Card>
      </div>
    </div>
  );
};