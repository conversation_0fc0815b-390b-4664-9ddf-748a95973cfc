import { useState, useEffect } from "react";
import { useNavigate } from "react-router-dom";
import { StationCard } from "@/components/StationCard";
import { Card } from "@/components/ui/card";
import { Input } from "@/components/ui/input";
import { Button } from "@/components/ui/button";
import { Badge } from "@/components/ui/badge";
import { 
  Search, 
  Filter, 
  ArrowLeft, 
  MapPin,
  Activity,
  AlertTriangle
} from "lucide-react";

// Extended mock data
const allStations = [
  {
    id: '1',
    name: 'Rajasthan Station 1',
    location: 'Jaipur District, Rajasthan',
    currentLevel: 8.5,
    status: 'safe' as const,
    lastUpdated: '2 hrs ago',
    district: 'Jaipur',
    state: 'Rajasthan'
  },
  {
    id: '2',
    name: 'Gujarat Station 2',
    location: 'Ahmedabad District, Gujarat',
    currentLevel: 3.2,
    status: 'moderate' as const,
    lastUpdated: '1 hr ago',
    district: 'Ahmedabad',
    state: 'Gujarat'
  },
  {
    id: '3',
    name: 'Punjab Station 3',
    location: 'Ludhiana District, Punjab',
    currentLevel: 1.8,
    status: 'critical' as const,
    lastUpdated: '30 min ago',
    district: 'Ludhiana',
    state: 'Punjab'
  },
  {
    id: '4',
    name: 'Maharashtra Station 4',
    location: 'Pune District, Maharashtra',
    currentLevel: 6.7,
    status: 'safe' as const,
    lastUpdated: '45 min ago',
    district: 'Pune',
    state: 'Maharashtra'
  },
  {
    id: '5',
    name: 'Karnataka Station 5',
    location: 'Bangalore District, Karnataka',
    currentLevel: 4.1,
    status: 'moderate' as const,
    lastUpdated: '20 min ago',
    district: 'Bangalore',
    state: 'Karnataka'
  },
  {
    id: '6',
    name: 'Tamil Nadu Station 6',
    location: 'Chennai District, Tamil Nadu',
    currentLevel: 1.2,
    status: 'critical' as const,
    lastUpdated: '15 min ago',
    district: 'Chennai',
    state: 'Tamil Nadu'
  }
];

export const StationList = () => {
  const navigate = useNavigate();
  const [searchQuery, setSearchQuery] = useState('');
  const [statusFilter, setStatusFilter] = useState<'all' | 'safe' | 'moderate' | 'critical'>('all');
  const [filteredStations, setFilteredStations] = useState(allStations);

  useEffect(() => {
    let filtered = allStations;

    // Filter by search query
    if (searchQuery) {
      filtered = filtered.filter(station =>
        station.name.toLowerCase().includes(searchQuery.toLowerCase()) ||
        station.location.toLowerCase().includes(searchQuery.toLowerCase()) ||
        station.district.toLowerCase().includes(searchQuery.toLowerCase()) ||
        station.state.toLowerCase().includes(searchQuery.toLowerCase())
      );
    }

    // Filter by status
    if (statusFilter !== 'all') {
      filtered = filtered.filter(station => station.status === statusFilter);
    }

    setFilteredStations(filtered);
  }, [searchQuery, statusFilter]);

  const getFilterCount = (status: 'safe' | 'moderate' | 'critical') => {
    return allStations.filter(s => s.status === status).length;
  };

  return (
    <div className="min-h-screen bg-background">
      {/* Header */}
      <div className="bg-gradient-primary text-white p-4">
        <div className="flex items-center justify-between mb-4">
          <div className="flex items-center">
            <Button
              variant="outline"
              size="sm"
              className="text-white border-white/30 mr-3"
              onClick={() => navigate('/dashboard')}
            >
              <ArrowLeft className="w-4 h-4" />
            </Button>
            <div>
              <h1 className="text-xl font-bold">All Stations</h1>
              <p className="text-sm opacity-90">{filteredStations.length} stations found</p>
            </div>
          </div>
        </div>
      </div>

      <div className="p-4 space-y-6">
        {/* Search */}
        <div className="relative">
          <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 text-muted-foreground w-4 h-4" />
          <Input
            placeholder="Search by station, district, or state..."
            value={searchQuery}
            onChange={(e) => setSearchQuery(e.target.value)}
            className="pl-10"
          />
        </div>

        {/* Filter Tabs */}
        <Card className="p-3 shadow-soft">
          <div className="flex items-center gap-2 mb-3">
            <Filter className="w-4 h-4 text-muted-foreground" />
            <span className="text-sm font-medium">Filter by Status:</span>
          </div>
          
          <div className="flex gap-2 overflow-x-auto">
            <Button
              variant={statusFilter === 'all' ? 'default' : 'outline'}
              size="sm"
              onClick={() => setStatusFilter('all')}
            >
              All ({allStations.length})
            </Button>
            <Button
              variant={statusFilter === 'safe' ? 'default' : 'outline'}
              size="sm"
              onClick={() => setStatusFilter('safe')}
              className={statusFilter === 'safe' ? 'bg-success hover:bg-success/90' : ''}
            >
              <Activity className="w-4 h-4 mr-1" />
              Safe ({getFilterCount('safe')})
            </Button>
            <Button
              variant={statusFilter === 'moderate' ? 'default' : 'outline'}
              size="sm"
              onClick={() => setStatusFilter('moderate')}
              className={statusFilter === 'moderate' ? 'bg-warning hover:bg-warning/90' : ''}
            >
              <AlertTriangle className="w-4 h-4 mr-1" />
              Moderate ({getFilterCount('moderate')})
            </Button>
            <Button
              variant={statusFilter === 'critical' ? 'default' : 'outline'}
              size="sm"
              onClick={() => setStatusFilter('critical')}
              className={statusFilter === 'critical' ? 'bg-critical hover:bg-critical/90' : ''}
            >
              <AlertTriangle className="w-4 h-4 mr-1" />
              Critical ({getFilterCount('critical')})
            </Button>
          </div>
        </Card>

        {/* Results Summary */}
        <div className="flex items-center justify-between">
          <p className="text-sm text-muted-foreground">
            Showing {filteredStations.length} of {allStations.length} stations
          </p>
          
          {filteredStations.length > 0 && (
            <div className="flex gap-2">
              <Badge variant="outline" className="bg-success/10 text-success">
                {filteredStations.filter(s => s.status === 'safe').length} Safe
              </Badge>
              <Badge variant="outline" className="bg-warning/10 text-warning">
                {filteredStations.filter(s => s.status === 'moderate').length} Moderate
              </Badge>
              <Badge variant="outline" className="bg-critical/10 text-critical">
                {filteredStations.filter(s => s.status === 'critical').length} Critical
              </Badge>
            </div>
          )}
        </div>

        {/* Station List */}
        <div className="space-y-4">
          {filteredStations.length > 0 ? (
            filteredStations.map((station) => (
              <StationCard
                key={station.id}
                id={station.id}
                name={station.name}
                location={station.location}
                currentLevel={station.currentLevel}
                status={station.status}
                lastUpdated={station.lastUpdated}
              />
            ))
          ) : (
            <Card className="p-8 text-center shadow-soft">
              <MapPin className="w-12 h-12 mx-auto mb-4 text-muted-foreground opacity-50" />
              <h3 className="text-lg font-semibold mb-2">No stations found</h3>
              <p className="text-muted-foreground mb-4">
                Try adjusting your search or filter criteria
              </p>
              <Button
                variant="outline"
                onClick={() => {
                  setSearchQuery('');
                  setStatusFilter('all');
                }}
              >
                Clear Filters
              </Button>
            </Card>
          )}
        </div>
      </div>
    </div>
  );
};