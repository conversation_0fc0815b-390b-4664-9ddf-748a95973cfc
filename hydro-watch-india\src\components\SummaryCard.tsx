import { Card } from "@/components/ui/card";
import { LucideIcon } from "lucide-react";

interface SummaryCardProps {
  title: string;
  value: string | number;
  subtitle?: string;
  icon: LucideIcon;
  variant?: 'default' | 'success' | 'warning' | 'critical';
  trend?: 'up' | 'down' | 'stable';
}

export const SummaryCard = ({ 
  title, 
  value, 
  subtitle, 
  icon: Icon, 
  variant = 'default',
  trend 
}: SummaryCardProps) => {
  const getVariantStyles = (variant: string) => {
    switch (variant) {
      case 'success': 
        return 'border-success/20 bg-gradient-to-br from-success/5 to-success/10';
      case 'warning': 
        return 'border-warning/20 bg-gradient-to-br from-warning/5 to-warning/10';
      case 'critical': 
        return 'border-critical/20 bg-gradient-to-br from-critical/5 to-critical/10';
      default: 
        return 'border-primary/20 bg-gradient-to-br from-primary/5 to-primary/10';
    }
  };

  const getIconColor = (variant: string) => {
    switch (variant) {
      case 'success': return 'text-success';
      case 'warning': return 'text-warning';
      case 'critical': return 'text-critical';
      default: return 'text-primary';
    }
  };

  const getTrendIcon = () => {
    switch (trend) {
      case 'up': return '↗';
      case 'down': return '↘';
      case 'stable': return '→';
      default: return null;
    }
  };

  return (
    <Card className={`p-4 shadow-soft card-hover-glow ${getVariantStyles(variant)} group`}>
      <div className="flex items-start justify-between">
        <div className="flex-1">
          <p className="text-sm font-medium text-muted-foreground mb-1 group-hover:text-foreground transition-colors">{title}</p>
          <div className="flex items-baseline gap-2">
            <span className="text-2xl font-bold text-foreground group-hover:gradient-text transition-all duration-300">{value}</span>
            {trend && (
              <span className={`text-sm ${getIconColor(variant)} group-hover:scale-110 transition-transform`}>
                {getTrendIcon()}
              </span>
            )}
          </div>
          {subtitle && (
            <p className="text-xs text-muted-foreground mt-1 group-hover:text-foreground/80 transition-colors">{subtitle}</p>
          )}
        </div>
        
        <div className={`p-2 rounded-lg ${getIconColor(variant)} bg-white/50 group-hover:bg-white/70 group-hover:scale-110 transition-all duration-300`}>
          <Icon className="w-5 h-5 group-hover:animate-pulse" />
        </div>
      </div>
    </Card>
  );
};