import { useState } from "react";
import { useNavigate } from "react-router-dom";
import { Card } from "@/components/ui/card";
import { Input } from "@/components/ui/input";
import { Button } from "@/components/ui/button";
import { Label } from "@/components/ui/label";
import { Badge } from "@/components/ui/badge";
import { Droplets, User, Shield, Microscope } from "lucide-react";
import groundwaterHero from "@/assets/groundwater-hero.jpg";

const roles = [
  { id: 'general', label: 'General User', icon: User, description: 'Farmers, Citizens' },
  { id: 'official', label: 'Official', icon: Shield, description: 'Government Officers' },
  { id: 'researcher', label: 'Researcher', icon: Microscope, description: 'Scientists, Analysts' }
];

export const Login = () => {
  const [selectedRole, setSelectedRole] = useState<string>('general');
  const [isLoading, setIsLoading] = useState(false);
  const navigate = useNavigate();

  const handleLogin = async () => {
    setIsLoading(true);
    // Simulate API call
    await new Promise(resolve => setTimeout(resolve, 1000));
    
    // Store user role and navigate to dashboard
    localStorage.setItem('userRole', selectedRole);
    navigate('/dashboard');
  };

  const handleDemoLogin = () => {
    localStorage.setItem('userRole', 'general');
    navigate('/dashboard');
  };

  return (
    <div className="min-h-screen bg-background flex flex-col">
      {/* Hero Section */}
      <div className="relative h-48 md:h-64 overflow-hidden">
        <img 
          src={groundwaterHero} 
          alt="Groundwater monitoring"
          className="w-full h-full object-cover"
        />
        <div className="absolute inset-0 bg-gradient-to-b from-primary/20 to-primary/60" />
        <div className="absolute inset-0 flex items-center justify-center">
          <div className="text-center text-white">
            <div className="flex items-center justify-center mb-4">
              <Droplets className="w-12 h-12 mr-3" />
              <h1 className="text-3xl md:text-4xl font-bold">GroundWatch</h1>
            </div>
            <p className="text-lg opacity-90">India Groundwater Monitoring</p>
          </div>
        </div>
      </div>

      {/* Login Form */}
      <div className="flex-1 p-4 flex items-center justify-center">
        <Card className="w-full max-w-md p-6 shadow-medium">
          <div className="text-center mb-6">
            <h2 className="text-2xl font-bold text-foreground mb-2">Welcome Back</h2>
            <p className="text-muted-foreground">Access real-time groundwater data</p>
          </div>

          <div className="space-y-4 mb-6">
            <div>
              <Label htmlFor="email">Email</Label>
              <Input 
                id="email" 
                type="email" 
                placeholder="Enter your email"
                className="mt-1"
              />
            </div>
            
            <div>
              <Label htmlFor="password">Password</Label>
              <Input 
                id="password" 
                type="password" 
                placeholder="Enter your password"
                className="mt-1"
              />
            </div>

            <div>
              <Label className="text-sm font-medium">Select Your Role</Label>
              <div className="grid grid-cols-1 gap-2 mt-2">
                {roles.map((role) => {
                  const Icon = role.icon;
                  return (
                    <div
                      key={role.id}
                      className={`p-3 rounded-lg border cursor-pointer transition-all ${
                        selectedRole === role.id
                          ? 'border-primary bg-primary/5'
                          : 'border-border hover:border-primary/50'
                      }`}
                      onClick={() => setSelectedRole(role.id)}
                    >
                      <div className="flex items-center justify-between">
                        <div className="flex items-center">
                          <Icon className="w-5 h-5 mr-3 text-primary" />
                          <div>
                            <p className="font-medium text-foreground">{role.label}</p>
                            <p className="text-sm text-muted-foreground">{role.description}</p>
                          </div>
                        </div>
                        {selectedRole === role.id && (
                          <Badge variant="outline" className="bg-primary text-primary-foreground">
                            Selected
                          </Badge>
                        )}
                      </div>
                    </div>
                  );
                })}
              </div>
            </div>
          </div>

          <div className="space-y-3">
            <Button 
              className="w-full"
              onClick={handleLogin}
              disabled={isLoading}
            >
              {isLoading ? 'Signing In...' : 'Sign In'}
            </Button>
            
            <Button 
              variant="outline" 
              className="w-full"
              onClick={handleDemoLogin}
            >
              Try Demo Mode
            </Button>
          </div>

          <div className="mt-6 text-center text-sm text-muted-foreground">
            <p>Demo credentials work for all roles</p>
          </div>
        </Card>
      </div>
    </div>
  );
};